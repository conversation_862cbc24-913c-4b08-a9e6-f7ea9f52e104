<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Share Comments</title>
    <meta name="csrf-token" content="test-token">
    <meta name="user-id" content="1">
    <meta name="user-is-admin" content="false">
    <meta name="user-role" content="user">
    <meta name="user-name" content="Test User">
    <meta name="user-avatar" content="">
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        .hidden { display: none; }
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-section { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .test-button { background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Debug Share Comments System</h1>
    
    <div class="debug-section">
        <h2>Debug Information</h2>
        <p>Open the browser console (F12) to see detailed debugging output.</p>
        <p>The debugging will show:</p>
        <ul>
            <li>🔵 Reply function calls</li>
            <li>🟡 Edit function calls</li>
            <li>🟢 HTML generation</li>
            <li>🟣 DOM manipulation</li>
            <li>🟠 Form submissions</li>
        </ul>

        <h3>Quick Console Commands for Real Dashboard:</h3>
        <p>Copy and paste these commands in your browser console on the actual dashboard page:</p>
        <div style="background: #333; color: #fff; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0;">
            <div>// Check all meta tags:</div>
            <div>Array.from(document.querySelectorAll('meta')).forEach((m,i) => console.log(`Meta ${i}: ${m.name}="${m.content}"`));</div>
            <br>
            <div>// Test user ID detection:</div>
            <div>console.log('User ID:', getCurrentUserId());</div>
            <br>
            <div>// Check authentication status:</div>
            <div>console.log('Auth meta:', document.querySelector('meta[name="user-id"]'));</div>
        </div>
    </div>

    <div class="debug-section">
        <h2>Test Functions</h2>
        <button class="test-button" onclick="testReplyFunction()">Test Reply Function</button>
        <button class="test-button" onclick="testEditFunction()">Test Edit Function</button>
        <button class="test-button" onclick="testCreateHTML()">Test HTML Creation</button>
        <button class="test-button" onclick="testEventListeners()">Test Event Listeners</button>
        <button class="test-button" onclick="testAlpineJS()">Test Alpine.js</button>
        <button class="test-button" onclick="testUserID()">Test User ID</button>
        <button class="test-button" onclick="checkAllMetaTags()">Check All Meta Tags</button>
    </div>

    <div class="debug-section">
        <h2>Mock Share Comment</h2>
        <div id="share-comments-list-1">
            <!-- Mock comment will be inserted here -->
        </div>
    </div>

    <script src="/js/comments.js"></script>
    <script src="/js/comment-modal.js"></script>
    <script>
        // Test functions
        function testReplyFunction() {
            console.log('🧪 Testing reply function...');
            if (typeof showShareReplyForm === 'function') {
                showShareReplyForm(123);
            } else {
                console.error('showShareReplyForm function not found');
            }
        }

        function testEditFunction() {
            console.log('🧪 Testing edit function...');
            if (typeof editShareComment === 'function') {
                editShareComment(123);
            } else {
                console.error('editShareComment function not found');
            }
        }

        function testCreateHTML() {
            console.log('🧪 Testing HTML creation...');
            if (typeof createShareCommentHTML === 'function') {
                const mockComment = {
                    id: 123,
                    content: 'Test comment',
                    user_id: 1,
                    user: {
                        id: 1,
                        name: 'Test User',
                        avatar: null
                    },
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    likes_count: 0
                };
                const html = createShareCommentHTML(mockComment, 1, false);
                console.log('Generated HTML:', html);
                document.getElementById('share-comments-list-1').innerHTML = html;
            } else {
                console.error('createShareCommentHTML function not found');
            }
        }

        function testEventListeners() {
            console.log('🧪 Testing event listeners...');
            console.log('initializeCommentEventListeners available:', typeof window.initializeCommentEventListeners);
            console.log('submitShareCommentEdit available:', typeof window.submitShareCommentEdit);
            console.log('submitShareCommentReply available:', typeof window.submitShareCommentReply);
        }

        function testUserID() {
            console.log('🧪 Testing user ID retrieval...');
            if (typeof getCurrentUserId === 'function') {
                const userId = getCurrentUserId();
                console.log('getCurrentUserId result:', userId);
                console.log('User ID type:', typeof userId);

                // Check meta tag directly
                const userMeta = document.querySelector('meta[name="user-id"]');
                console.log('User meta tag:', userMeta);
                console.log('User meta content:', userMeta ? userMeta.getAttribute('content') : 'not found');
            } else {
                console.error('getCurrentUserId function not found');
            }
        }

        function checkAllMetaTags() {
            console.log('🧪 Checking all meta tags...');
            const allMetas = document.querySelectorAll('meta');
            console.log('Total meta tags found:', allMetas.length);

            allMetas.forEach((meta, index) => {
                const name = meta.getAttribute('name');
                const content = meta.getAttribute('content');
                const property = meta.getAttribute('property');

                if (name) {
                    console.log(`Meta ${index}: name="${name}" content="${content}"`);
                } else if (property) {
                    console.log(`Meta ${index}: property="${property}" content="${content}"`);
                } else {
                    console.log(`Meta ${index}:`, meta.outerHTML);
                }
            });

            // Specifically look for user-related meta tags
            console.log('🔍 Looking for user-related meta tags...');
            const userRelated = document.querySelectorAll('meta[name*="user"], meta[name*="auth"], meta[name*="current"]');
            console.log('User-related meta tags:', userRelated);
        }

        function testAlpineJS() {
            console.log('🧪 Testing Alpine.js...');
            console.log('Alpine.js available:', typeof window.Alpine);
            if (window.Alpine) {
                console.log('Alpine.js version:', window.Alpine.version);
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            console.log('🧪 Auto-running tests...');
            setTimeout(() => {
                checkAllMetaTags();
                testUserID();
                testEventListeners();
                testAlpineJS();
                testCreateHTML();
            }, 1000);
        });
    </script>
</body>
</html>
