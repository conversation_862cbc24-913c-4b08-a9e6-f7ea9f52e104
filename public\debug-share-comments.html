<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Share Comments</title>
    <meta name="csrf-token" content="test-token">
    <meta name="user-id" content="1">
    <meta name="user-is-admin" content="false">
    <meta name="user-role" content="user">
    <meta name="user-name" content="Test User">
    <meta name="user-avatar" content="">
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        .hidden { display: none; }
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-section { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .test-button { background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Debug Share Comments System</h1>
    
    <div class="debug-section">
        <h2>Debug Information</h2>
        <p>Open the browser console (F12) to see detailed debugging output.</p>
        <p>The debugging will show:</p>
        <ul>
            <li>🔵 Reply function calls</li>
            <li>🟡 Edit function calls</li>
            <li>🟢 HTML generation</li>
            <li>🟣 DOM manipulation</li>
            <li>🟠 Form submissions</li>
        </ul>
    </div>

    <div class="debug-section">
        <h2>Test Functions</h2>
        <button class="test-button" onclick="testReplyFunction()">Test Reply Function</button>
        <button class="test-button" onclick="testEditFunction()">Test Edit Function</button>
        <button class="test-button" onclick="testCreateHTML()">Test HTML Creation</button>
        <button class="test-button" onclick="testEventListeners()">Test Event Listeners</button>
        <button class="test-button" onclick="testAlpineJS()">Test Alpine.js</button>
    </div>

    <div class="debug-section">
        <h2>Mock Share Comment</h2>
        <div id="share-comments-list-1">
            <!-- Mock comment will be inserted here -->
        </div>
    </div>

    <script src="/js/comments.js"></script>
    <script src="/js/comment-modal.js"></script>
    <script>
        // Test functions
        function testReplyFunction() {
            console.log('🧪 Testing reply function...');
            if (typeof showShareReplyForm === 'function') {
                showShareReplyForm(123);
            } else {
                console.error('showShareReplyForm function not found');
            }
        }

        function testEditFunction() {
            console.log('🧪 Testing edit function...');
            if (typeof editShareComment === 'function') {
                editShareComment(123);
            } else {
                console.error('editShareComment function not found');
            }
        }

        function testCreateHTML() {
            console.log('🧪 Testing HTML creation...');
            if (typeof createShareCommentHTML === 'function') {
                const mockComment = {
                    id: 123,
                    content: 'Test comment',
                    user_id: 1,
                    user: {
                        id: 1,
                        name: 'Test User',
                        avatar: null
                    },
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    likes_count: 0
                };
                const html = createShareCommentHTML(mockComment, 1, false);
                console.log('Generated HTML:', html);
                document.getElementById('share-comments-list-1').innerHTML = html;
            } else {
                console.error('createShareCommentHTML function not found');
            }
        }

        function testEventListeners() {
            console.log('🧪 Testing event listeners...');
            console.log('initializeCommentEventListeners available:', typeof window.initializeCommentEventListeners);
            console.log('submitShareCommentEdit available:', typeof window.submitShareCommentEdit);
            console.log('submitShareCommentReply available:', typeof window.submitShareCommentReply);
        }

        function testAlpineJS() {
            console.log('🧪 Testing Alpine.js...');
            console.log('Alpine.js available:', typeof window.Alpine);
            if (window.Alpine) {
                console.log('Alpine.js version:', window.Alpine.version);
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            console.log('🧪 Auto-running tests...');
            setTimeout(() => {
                testEventListeners();
                testAlpineJS();
                testCreateHTML();
            }, 1000);
        });
    </script>
</body>
</html>
